/**
 * Firebase AI Chat Service
 * Handles AI chat using Firebase AI Logic instead of external API calls
 */

import {
  ChatMessage,
  AIModelSettings,
  ChatError,
  AIChatConfig
} from '../../types/aiChat';
import { ai } from '../../firebase/config';
import { getGenerativeModel, FunctionCallingMode } from 'firebase/ai';
import { logger } from '../../utils/logger';
import { DEFAULT_ECONOMIC_EVENT_FILTER_SETTINGS } from '../../components/economicCalendar/EconomicCalendarDrawer';
import { tradingAnalysisFunctions, TradingAnalysisResult } from './tradingAnalysisFunctions';
import { Trade } from '../../types/trade';
import { Calendar } from '../../types/calendar';
import { getSystemPrompt } from './aiSystemPrompt';
import { getFunctionDeclarations } from './aiFunctionDeclarations';

// Function result caching for large data handling
interface CachedResult {
  data: any;
  timestamp: number;
  functionName: string;
}

class FunctionResultCache {
  private static readonly CACHE_PREFIX = 'ai_function_result_';
  private static readonly CACHE_EXPIRY = 5 * 60 * 1000; // 5 minutes

  static store(functionName: string, result: any): string {
    const key = `${this.CACHE_PREFIX}${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const cachedResult: CachedResult = {
      data: result,
      timestamp: Date.now(),
      functionName
    };

    try {
      localStorage.setItem(key, JSON.stringify(cachedResult));
      logger.log(`Stored large function result for ${functionName} with key: ${key}`);
      return key;
    } catch (error) {
      logger.error('Failed to store function result in localStorage:', error);
      return '';
    }
  }

  static retrieve(key: string): any {
    try {
      const item = localStorage.getItem(key);
      if (!item) {
        logger.warn(`Function result cache miss for key: ${key}`);
        return null;
      }

      const cachedResult: CachedResult = JSON.parse(item);

      // Check if expired
      if (Date.now() - cachedResult.timestamp > this.CACHE_EXPIRY) {
        localStorage.removeItem(key);
        logger.warn(`Function result cache expired for key: ${key}`);
        return null;
      }

      logger.log(`Retrieved cached result for ${cachedResult.functionName} from key: ${key}`);
      return cachedResult.data;
    } catch (error) {
      logger.error('Failed to retrieve function result from localStorage:', error);
      return null;
    }
  }

  static clear(key: string): void {
    try {
      localStorage.removeItem(key);
      logger.log(`Cleared function result cache for key: ${key}`);
    } catch (error) {
      logger.error('Failed to clear function result cache:', error);
    }
  }

  static clearExpired(): void {
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith(this.CACHE_PREFIX));
      let cleared = 0;

      keys.forEach(key => {
        const item = localStorage.getItem(key);
        if (item) {
          try {
            const cachedResult: CachedResult = JSON.parse(item);
            if (Date.now() - cachedResult.timestamp > this.CACHE_EXPIRY) {
              localStorage.removeItem(key);
              cleared++;
            }
          } catch {
            // Invalid format, remove it
            localStorage.removeItem(key);
            cleared++;
          }
        }
      });

      if (cleared > 0) {
        logger.log(`Cleared ${cleared} expired function result cache entries`);
      }
    } catch (error) {
      logger.error('Failed to clear expired function result cache:', error);
    }
  }
}

class FirebaseAIChatService {
  private readonly SYSTEM_PROMPT = getSystemPrompt();
  private lastInitializedCalendarId: string | null = null;
  private lastInitializedTradesCount: number = 0;
  private static readonly LARGE_RESULT_THRESHOLD = 10000; // 10KB threshold for caching

  /**
   * Determines if a function result is too large and should be cached
   */
  private isResultTooLarge(result: any): boolean {
    try {
      const resultString = JSON.stringify(result);
      return resultString.length > FirebaseAIChatService.LARGE_RESULT_THRESHOLD;
    } catch {
      return false;
    }
  }

  /**
   * Processes function result - caches large results and returns appropriate response
   */
  private processLargeResult(functionName: string, result: any, isLastFunction: boolean, totalFunctions: number): any {
    // Clear expired cache entries periodically
    FunctionResultCache.clearExpired();

    // If this is the last function or there's only one function, return the full result
    if (isLastFunction || totalFunctions === 1) {
      logger.log(`Returning full result for ${functionName} (last function or single function)`);
      return result;
    }

    // Check if result is too large
    if (this.isResultTooLarge(result)) {
      const cacheKey = FunctionResultCache.store(functionName, result);
      if (cacheKey) {
        logger.log(`Cached large result for ${functionName}, returning cache key`);

        // Create a summary of what was cached to help the AI understand
        let summary = `Large result cached for ${functionName}. Use cache key ${cacheKey} to retrieve data in subsequent functions.`;

        // Add specific information about the cached data structure
        if (result && typeof result === 'object' && result.success && result.data) {
          if (result.data.trades && Array.isArray(result.data.trades)) {
            summary += ` Contains ${result.data.trades.length} trades.`;
          } else if (Array.isArray(result.data)) {
            summary += ` Contains ${result.data.length} items.`;
          }
        }

        return {
          success: true,
          cached: true,
          cacheKey: cacheKey,
          summary: summary,
          resultSize: JSON.stringify(result).length,
          // Include metadata about the cached data structure
          dataStructure: this.describeCachedDataStructure(result)
        };
      } else {
        logger.warn(`Failed to cache large result for ${functionName}, returning truncated result`);
        return {
          success: true,
          cached: false,
          summary: `Large result for ${functionName} could not be cached. Result truncated.`,
          resultSize: JSON.stringify(result).length,
          data: result // Fallback to original result
        };
      }
    }

    // Result is small enough, return as-is
    return result;
  }

  /**
   * Describe the structure of cached data to help with debugging
   */
  private describeCachedDataStructure(result: any): string {
    if (!result || typeof result !== 'object') {
      return 'primitive';
    }

    if (result.success && result.data) {
      if (result.data.trades && Array.isArray(result.data.trades)) {
        return `function_result_with_trades_array(${result.data.trades.length})`;
      } else if (Array.isArray(result.data)) {
        return `function_result_with_array(${result.data.length})`;
      } else {
        return 'function_result_with_object';
      }
    } else if (Array.isArray(result)) {
      return `array(${result.length})`;
    } else {
      return 'object';
    }
  }

  /**
   * Send a chat message with AI-driven function calling
   */
  async sendMessageWithFunctionCalling(
    message: string,
    trades: Trade[],
    calendar: Calendar,
    conversationHistory: ChatMessage[] = [],
    modelSettings?: AIModelSettings,
    config?: AIChatConfig
  ): Promise<{ response: string; tokenCount?: number; functionCalls?: any[] }> {
    try {
      logger.log('Sending message with AI-driven function calling...');

      // Initialize trading analysis functions with current data
      tradingAnalysisFunctions.initialize(trades, calendar, config?.maxContextTrades || 100);

      // Prepare messages for function calling
      const messages = this.prepareFunctionCallingMessages(message, conversationHistory);

      // Get the model to use - gemini-2.5-flash is recommended for function calling
      const modelName = modelSettings?.model || 'gemini-2.5-flash';
      logger.log(`Using model ${modelName} for function calling`);

      // Create generative model instance with function declarations
      const model = getGenerativeModel(ai, {
        model: modelName,
        // Add system instruction following official guidelines
        systemInstruction: this.SYSTEM_PROMPT,
        generationConfig: {
          // Use lower temperature for more deterministic function calls (official recommendation)
          temperature: modelSettings?.settings?.temperature || 0.3,
          maxOutputTokens: modelSettings?.settings?.maxTokens || 8000,
          topP: modelSettings?.settings?.topP || 1
        },
        tools: [{
          functionDeclarations: getFunctionDeclarations(calendar.economicCalendarFilters?.currencies || DEFAULT_ECONOMIC_EVENT_FILTER_SETTINGS.currencies)
        }],
        // Use AUTO mode by default - model decides when to use functions
        toolConfig: {
          functionCallingConfig: {
            mode: FunctionCallingMode.AUTO
          }
        }
      });

      // Convert messages to a simple prompt string for function calling
      const prompt = messages.map(msg => `${msg.role}: ${msg.content}`).join('\n');

      // Use official compositional function calling pattern
      let finalResponse = '';
      const executedFunctions: any[] = [];

      // Create a chat session for natural function calling flow
      const chat = model.startChat();
      let currentResponse = await chat.sendMessage(prompt);

      // Support up to 10 rounds of function calling (increased for complex analysis)
      const maxRounds = 10;
      let currentRound = 0;

      // Natural compositional function calling loop
      while (currentRound < maxRounds) {
        // Check for function calls using the standard Firebase AI SDK method
        const functionCalls = currentResponse.response.functionCalls();

        if (!functionCalls || functionCalls.length === 0) {
          // No more function calls needed - model has final response
          finalResponse = currentResponse.response.text() || 'No response received';
          break;
        }

        currentRound++;
        logger.log(`AI requested ${functionCalls.length} function calls (round ${currentRound}):`,
          functionCalls.map(fc => `${fc.name}(${Object.keys(fc.args || {}).join(', ')})`));

        // Execute all function calls for this round
        const functionResponseParts: any[] = [];
        const totalFunctionsInRound = functionCalls.length;

        for (let i = 0; i < functionCalls.length; i++) {
          const functionCall = functionCalls[i];
          const isLastFunction = i === functionCalls.length - 1;

          try {
            const result = await this.executeFunctionCall({
              name: functionCall.name,
              args: functionCall.args
            });

            executedFunctions.push({
              name: functionCall.name,
              args: functionCall.args,
              result: result,
              round: currentRound
            });

            // Add trade card reminder for findSimilarTrades
            let processedResult = result;
            if (functionCall.name === 'findSimilarTrades' && result?.success && result?.data?.trades) {
              processedResult = {
                ...result,
                data: {
                  ...result.data,
                  _reminder: "IMPORTANT: Focus on analysis and insights rather than listing individual trade details."
                }
              };
            }

            // Process large results using caching system
            const finalResult = this.processLargeResult(
              functionCall.name,
              processedResult,
              isLastFunction,
              totalFunctionsInRound
            );

            // Ensure we have a valid result before adding to response parts
            const validResult = finalResult || { success: false, error: 'Function returned undefined' };

            // Create function response part with defensive checks
            const functionResponsePart = {
              functionResponse: {
                name: functionCall.name,
                response: validResult
              }
            };

            // Validate the structure before adding to avoid malformed responses
            if (functionResponsePart.functionResponse &&
                functionResponsePart.functionResponse.name &&
                functionResponsePart.functionResponse.response !== undefined) {
              functionResponseParts.push(functionResponsePart);
            } else {
              logger.error('Failed to create valid function response part:', functionResponsePart);
              // Add a fallback response to maintain the expected number of responses
              functionResponseParts.push({
                functionResponse: {
                  name: functionCall.name || 'unknown',
                  response: { success: false, error: 'Invalid function response structure' }
                }
              });
            }

          } catch (error) {
            logger.error(`Error executing function ${functionCall.name}:`, error);
            const errorResult = {
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            };

            executedFunctions.push({
              name: functionCall.name,
              args: functionCall.args || {},
              result: errorResult,
              round: currentRound
            });

            // Ensure we have a valid error result
            const validErrorResult = errorResult || { success: false, error: 'Unknown function execution error' };

            // Create error function response part with defensive checks
            const errorResponsePart = {
              functionResponse: {
                name: functionCall.name,
                response: validErrorResult
              }
            };

            // Validate the error response structure before adding
            if (errorResponsePart.functionResponse &&
                errorResponsePart.functionResponse.name &&
                errorResponsePart.functionResponse.response !== undefined) {
              functionResponseParts.push(errorResponsePart);
            } else {
              logger.error('Failed to create valid error response part:', errorResponsePart);
              // Add a fallback error response
              functionResponseParts.push({
                functionResponse: {
                  name: functionCall.name || 'unknown',
                  response: { success: false, error: 'Invalid error response structure' }
                }
              });
            }
          }
        }

        try {
          // Send function responses back to model
          // According to Firebase AI docs, function responses should be sent directly as parts array
          logger.log(`Sending ${functionResponseParts.length} function responses to AI`);

          // Defensive programming: ensure we have valid function response parts
          if (!functionResponseParts || functionResponseParts.length === 0) {
            logger.error('No function response parts to send to AI');
            finalResponse = 'I encountered an error processing the function results. Please try rephrasing your question.';
            break;
          }

          // Enhanced validation based on Firebase_Gemini_AI_SDK_Function_Call_Error.md
          // Check for empty or null parts and ensure correct response formatting
          const validResponseParts = functionResponseParts.filter(part => {
            if (!part || !part.functionResponse) {
              logger.warn('Invalid function response part - missing functionResponse:', part);
              return false;
            }
            if (!part.functionResponse.name || typeof part.functionResponse.name !== 'string') {
              logger.warn('Invalid function response part - missing or invalid name:', part.functionResponse);
              return false;
            }
            if (part.functionResponse.response === undefined || part.functionResponse.response === null) {
              logger.warn('Invalid function response part - missing response:', part.functionResponse);
              return false;
            }
            return true;
          });

          if (validResponseParts.length === 0) {
            logger.error('No valid function response parts found after enhanced validation');
            finalResponse = 'I encountered an error processing the function results. Please try rephrasing your question.';
            break;
          }

          // Ensure the number of function response parts matches the number of function calls
          if (validResponseParts.length !== functionCalls.length) {
            logger.warn(`Mismatch: ${functionCalls.length} function calls but ${validResponseParts.length} valid responses`);
          }

          logger.log('Sending valid function response parts:', validResponseParts.map(p => p.functionResponse.name));
          logger.log('Function response structure validation passed');

          currentResponse = await chat.sendMessage(validResponseParts);

        } catch (sendError) {
          logger.error('Error sending function responses to AI:', sendError);

          // Handle the specific "Cannot read properties of undefined (reading 'parts')" error
          if (sendError instanceof Error && sendError.message.includes('Cannot read properties of undefined (reading \'parts\')')) {
            logger.error('Detected Firebase AI SDK "parts" error - this is a known issue with multiple function calls');
            logger.error('Function response parts that caused the error:', JSON.stringify(functionResponseParts, null, 2));

            // Try to recover by providing a summary of what was executed
            const executedFunctionNames = functionResponseParts.map(p => p.functionResponse?.name).filter(Boolean);
            finalResponse = `I successfully executed ${executedFunctionNames.length} functions (${executedFunctionNames.join(', ')}) and gathered the requested information. However, there was a technical issue with the response formatting. The analysis was completed successfully.`;
            break;
          }

          finalResponse = 'I encountered an error while processing the function results. Please try rephrasing your question.';
          break;
        }
      }

      // Handle max rounds reached
      if (currentRound >= maxRounds && !finalResponse) {
        finalResponse = currentResponse.response.text() || 'Analysis completed. The system made multiple function calls to gather comprehensive information.';
      }

      logger.log('Received response from Firebase AI Logic with function calling');

      return {
        response: finalResponse,
        tokenCount: currentResponse.response.usageMetadata?.totalTokenCount,
        functionCalls: executedFunctions
      };

    } catch (error) {
      logger.error('Error in sendMessageWithFunctionCalling:', error);
      throw this.createNetworkError(error);
    }
  }









  /**
   * Prepare messages for function calling
   */
  private prepareFunctionCallingMessages(
    message: string,
    conversationHistory: ChatMessage[]
  ): Array<{ role: 'system' | 'user' | 'assistant'; content: string }> {
    const messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }> = [];

    // Add conversation history (system instruction is now set at model level)
    for (const historyMessage of conversationHistory) {
      if (historyMessage.role !== 'system') {
        messages.push({
          role: historyMessage.role as 'user' | 'assistant',
          content: historyMessage.content
        });
      }
    }

    // Add current user message with technical context
    const contextualMessage = this.addTechnicalContext(message);
    messages.push({
      role: 'user',
      content: contextualMessage
    });

    return messages;
  }

  /**
   * Add technical context to user message when needed
   */
  private addTechnicalContext(message: string): string {
    return `${message}

## NOTE
If you want to display specific trades as cards, Use convertTradeIdsToCards function to generate JSON. the system will parse the JSON and display the cards automatically.
`;
  }





  /**
   * Process function arguments to handle cache keys
   */
  private processFunctionArgs(args: any): any {
    if (!args || typeof args !== 'object') {
      return args;
    }

    const processedArgs = { ...args };
    logger.log('Processing function arguments:', processedArgs);

    // Look for cache keys in arguments and replace with actual data
    for (const [key, value] of Object.entries(processedArgs)) {
      if (typeof value === 'string' && value.startsWith(FunctionResultCache['CACHE_PREFIX'])) {
        logger.log(`Found cache key in argument ${key}: ${value}`);
        const cachedData = FunctionResultCache.retrieve(value);
        if (cachedData) {
          // The cached data might be a full function result with success/data structure
          // Extract the actual data we need
          let actualData = cachedData;
          if (cachedData && typeof cachedData === 'object' && cachedData.success && cachedData.data) {
            actualData = cachedData.data;
          }

          // For extractTradeIds function, we need the trades array specifically
          if (key === 'trades' && actualData && typeof actualData === 'object') {
            if (actualData.trades && Array.isArray(actualData.trades)) {
              processedArgs[key] = actualData.trades;
            } else if (Array.isArray(actualData)) {
              processedArgs[key] = actualData;
            } else {
              logger.warn(`Cached data for ${key} doesn't contain expected trades array:`, actualData);
              processedArgs[key] = [];
            }
          } else {
            processedArgs[key] = actualData;
          }

          // Clear the cache after retrieval to free up space
          FunctionResultCache.clear(value);
          logger.log(`Retrieved and cleared cached data for argument ${key}`, {
            dataType: typeof actualData,
            isArray: Array.isArray(actualData),
            hasTradesProperty: actualData && typeof actualData === 'object' && 'trades' in actualData
          });
        } else {
          logger.warn(`Failed to retrieve cached data for key: ${value}`);
          // For critical parameters like 'trades', provide an empty array as fallback
          if (key === 'trades') {
            logger.warn(`Setting empty array for missing trades cache key`);
            processedArgs[key] = [];
          }
        }
      }
      // Handle nested objects
      else if (typeof value === 'object' && value !== null) {
        processedArgs[key] = this.processFunctionArgs(value);
      }
    }

    return processedArgs;
  }
  /**
   * Execute a function call requested by the AI
   */
  private async executeFunctionCall(call: any): Promise<TradingAnalysisResult> {
    try {
      logger.log(`Executing function call: ${call.name}`, call.args);

      // Process arguments to handle cache keys
      const processedArgs = this.processFunctionArgs(call.args);

      switch (call.name) {
        case 'searchTrades':
          return await tradingAnalysisFunctions.searchTrades(processedArgs);

        case 'getTradeStatistics':
          return await tradingAnalysisFunctions.getTradeStatistics(processedArgs);

        case 'findSimilarTrades':
          return await tradingAnalysisFunctions.findSimilarTrades(processedArgs);

        case 'queryDatabase':
          return await tradingAnalysisFunctions.queryDatabase(processedArgs);

        case 'analyzeEconomicEvents':
          return await tradingAnalysisFunctions.analyzeEconomicEvents(processedArgs);

        case 'fetchEconomicEvents':
          return await tradingAnalysisFunctions.fetchEconomicEvents(processedArgs);

        case 'extractTradeIds':
          return await tradingAnalysisFunctions.extractTradeIds(processedArgs);

        case 'convertTradeIdsToCards':
          return await tradingAnalysisFunctions.convertTradeIdsToCards(processedArgs);

        default:
          return {
            success: false,
            error: `Unknown function: ${call.name}`
          };
      }
    } catch (error) {
      logger.error(`Error executing function ${call.name}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Create network error
   */
  private createNetworkError(error: any): ChatError {
    return {
      type: 'network_error',
      message: 'Failed to communicate with Firebase AI Logic',
      details: error?.message || 'Unknown error occurred',
      retryable: true
    };
  }
}

// Export singleton instance
export const firebaseAIChatService = new FirebaseAIChatService();
